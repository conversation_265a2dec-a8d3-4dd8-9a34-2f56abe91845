#!/usr/bin/env python3
"""
PII Detector and Redactor for Project Guardian 2.0
Detects and redacts PII from CSV data according to specified definitions.
"""

import csv
import json
import re
import sys
from typing import Dict, List, Tuple, Any


class PIIDetector:
    def __init__(self):
        # Regex patterns for standalone PII
        self.phone_pattern = re.compile(r'\b\d{10}\b')
        self.aadhar_pattern = re.compile(r'\b\d{4}\s?\d{4}\s?\d{4}\b|\b\d{12}\b')
        self.passport_pattern = re.compile(r'\b[A-Z]\d{7}\b|\b[A-Z]{2}\d{6}\b')
        self.upi_pattern = re.compile(r'\b[\w\d]+@[\w\d]+\b|\b\d{10}@[\w\d]+\b')
        
        # Regex patterns for combinatorial PII
        self.email_pattern = re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b')
        self.name_pattern = re.compile(r'^[A-Z][a-z]+ [A-Z][a-z]+(?:\s[A-Z][a-z]+)*$')
        self.address_pattern = re.compile(r'.+,\s*.+,\s*\d{6}')
        self.ip_pattern = re.compile(r'\b(?:\d{1,3}\.){3}\d{1,3}\b')
        
        # Combinatorial PII fields
        self.combinatorial_fields = {'name', 'email', 'address', 'device_id', 'ip_address'}
        
    def is_standalone_pii(self, field: str, value: str) -> bool:
        """Check if a field contains standalone PII"""
        if field == 'phone' and self.phone_pattern.match(str(value)):
            return True
        if field == 'aadhar' and self.aadhar_pattern.match(str(value)):
            return True
        if field == 'passport' and self.passport_pattern.match(str(value)):
            return True
        if field == 'upi_id' and self.upi_pattern.match(str(value)):
            return True
        return False
    
    def is_valid_name(self, value: str) -> bool:
        """Check if value is a valid full name (first + last name)"""
        return bool(self.name_pattern.match(str(value)))
    
    def is_valid_email(self, value: str) -> bool:
        """Check if value is a valid email"""
        return bool(self.email_pattern.match(str(value)))
    
    def is_valid_address(self, value: str) -> bool:
        """Check if value is a valid address with street, city, pincode"""
        return bool(self.address_pattern.search(str(value)))
    
    def is_valid_device_id(self, value: str) -> bool:
        """Check if value looks like a device ID"""
        # Device IDs typically contain alphanumeric characters
        return bool(re.match(r'^[A-Za-z0-9]{6,}$', str(value)))
    
    def is_valid_ip(self, value: str) -> bool:
        """Check if value is a valid IP address"""
        return bool(self.ip_pattern.match(str(value)))
    
    def detect_combinatorial_pii(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Detect combinatorial PII in a record"""
        pii_fields = []
        
        # Check each field for combinatorial PII
        for field, value in data.items():
            if field == 'name' and self.is_valid_name(value):
                pii_fields.append(field)
            elif field == 'email' and self.is_valid_email(value):
                pii_fields.append(field)
            elif field == 'address' and self.is_valid_address(value):
                pii_fields.append(field)
            elif field == 'device_id' and self.is_valid_device_id(value):
                pii_fields.append(field)
            elif field == 'ip_address' and self.is_valid_ip(value):
                pii_fields.append(field)
        
        # PII if 2 or more combinatorial fields present
        return len(pii_fields) >= 2, pii_fields
    
    def redact_phone(self, phone: str) -> str:
        """Redact phone number keeping first 2 and last 2 digits"""
        phone_str = str(phone)
        if len(phone_str) == 10:
            return f"{phone_str[:2]}XXXXXX{phone_str[-2:]}"
        return "[REDACTED_PHONE]"
    
    def redact_aadhar(self, aadhar: str) -> str:
        """Redact Aadhar number keeping first 4 and last 4 digits"""
        aadhar_clean = re.sub(r'\s', '', str(aadhar))
        if len(aadhar_clean) == 12:
            return f"{aadhar_clean[:4]}XXXX{aadhar_clean[-4:]}"
        return "[REDACTED_AADHAR]"
    
    def redact_passport(self, passport: str) -> str:
        """Redact passport number"""
        return "[REDACTED_PASSPORT]"
    
    def redact_upi(self, upi: str) -> str:
        """Redact UPI ID"""
        return "[REDACTED_UPI]"
    
    def redact_name(self, name: str) -> str:
        """Redact name keeping first letter of each word"""
        words = str(name).split()
        redacted_words = [f"{word[0]}{'X' * (len(word) - 1)}" for word in words]
        return " ".join(redacted_words)
    
    def redact_email(self, email: str) -> str:
        """Redact email keeping first 2 chars and domain"""
        email_str = str(email)
        if '@' in email_str:
            local, domain = email_str.split('@', 1)
            if len(local) > 2:
                redacted_local = f"{local[:2]}{'X' * (len(local) - 2)}"
            else:
                redacted_local = 'XX'
            return f"{redacted_local}@{domain}"
        return "[REDACTED_EMAIL]"
    
    def redact_address(self, address: str) -> str:
        """Redact address"""
        return "[REDACTED_ADDRESS]"
    
    def redact_device_id(self, device_id: str) -> str:
        """Redact device ID"""
        return "[REDACTED_DEVICE_ID]"
    
    def redact_ip(self, ip: str) -> str:
        """Redact IP address"""
        return "[REDACTED_IP]"
    
    def process_record(self, record_data: Dict[str, Any]) -> Tuple[Dict[str, Any], bool]:
        """Process a single record and return redacted data and PII flag"""
        redacted_data = record_data.copy()
        has_pii = False
        pii_fields = []
        
        # Check for standalone PII
        for field, value in record_data.items():
            if self.is_standalone_pii(field, value):
                has_pii = True
                pii_fields.append(field)
                
                # Redact based on field type
                if field == 'phone':
                    redacted_data[field] = self.redact_phone(value)
                elif field == 'aadhar':
                    redacted_data[field] = self.redact_aadhar(value)
                elif field == 'passport':
                    redacted_data[field] = self.redact_passport(value)
                elif field == 'upi_id':
                    redacted_data[field] = self.redact_upi(value)
        
        # Check for combinatorial PII
        is_combinatorial_pii, combinatorial_fields = self.detect_combinatorial_pii(record_data)
        
        if is_combinatorial_pii:
            has_pii = True
            pii_fields.extend(combinatorial_fields)
            
            # Redact combinatorial PII fields
            for field in combinatorial_fields:
                value = record_data[field]
                if field == 'name':
                    redacted_data[field] = self.redact_name(value)
                elif field == 'email':
                    redacted_data[field] = self.redact_email(value)
                elif field == 'address':
                    redacted_data[field] = self.redact_address(value)
                elif field == 'device_id':
                    redacted_data[field] = self.redact_device_id(value)
                elif field == 'ip_address':
                    redacted_data[field] = self.redact_ip(value)
        
        return redacted_data, has_pii


def main():
    if len(sys.argv) != 2:
        print("Usage: python3 detector_full_kguru.py <input_csv_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = "redacted_output_kguru.csv"
    
    detector = PIIDetector()
    
    try:
        with open(input_file, 'r', encoding='utf-8') as infile, \
             open(output_file, 'w', encoding='utf-8', newline='') as outfile:
            
            reader = csv.DictReader(infile)
            writer = csv.writer(outfile)
            
            # Write header
            writer.writerow(['record_id', 'redacted_data_json', 'is_pii'])
            
            for row in reader:
                record_id = row['record_id']
                data_json = row['data_json']
                
                try:
                    # Clean and parse JSON data
                    cleaned_json = data_json.strip().rstrip('"')
                    if cleaned_json.endswith('""'):
                        cleaned_json = cleaned_json[:-1]

                    data = json.loads(cleaned_json)

                    # Process the record
                    redacted_data, is_pii = detector.process_record(data)

                    # Convert back to JSON
                    redacted_json = json.dumps(redacted_data, separators=(',', ': '))

                    # Write to output
                    writer.writerow([record_id, redacted_json, is_pii])

                except json.JSONDecodeError as e:
                    print(f"Error parsing JSON in record {record_id}: {e}")
                    # Try to fix common JSON issues
                    try:
                        # Remove extra quotes and fix common issues
                        fixed_json = data_json.strip().strip('"')
                        if ': 2024-' in fixed_json and '"2024-' not in fixed_json:
                            fixed_json = re.sub(r': (2024-\d{2}-\d{2})', r': "\1"', fixed_json)
                        data = json.loads(fixed_json)
                        redacted_data, is_pii = detector.process_record(data)
                        redacted_json = json.dumps(redacted_data, separators=(',', ': '))
                        writer.writerow([record_id, redacted_json, is_pii])
                    except:
                        writer.writerow([record_id, data_json, False])
                except Exception as e:
                    print(f"Error processing record {record_id}: {e}")
                    writer.writerow([record_id, data_json, False])
    
    except FileNotFoundError:
        print(f"Error: Input file '{input_file}' not found.")
        sys.exit(1)
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
    
    print(f"Processing complete. Output saved to '{output_file}'")


if __name__ == "__main__":
    main()
